import React, { useState, useRef, useMemo, useCallback, memo, useEffect } from 'react';
import {
  Button, Space, Divider, Row, Col, TreeSelect, InputNumber,
  Popconfirm, App, Input, Typography, Layout
} from 'antd';
import {
  EditOutlined, CloseOutlined, <PERSON>boltOutlined, SaveOutlined, ClearOutlined, RobotOutlined, FileTextOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import _, { isEqual, isEmpty, cloneDeep } from 'lodash'; // Import cloneDeep
// import OpenAI from 'openai'; // OpenAI import might be redundant
import { useStore } from '../stores/index.js';
import { shallow } from 'zustand/shallow';
import { sliceContentByIds, html2content, content2Html, checkTagCountMismatch, getFirstTranslatedItemText, getAllTextFromChunks, corsFailedApiUrls, getOpenAIClientForCalling, parseThinkContent, extractReasoningFromChunk, formatTextWithAI, streamTextWithAI } from '../utils/index.js'; // Import new utils, added streamTextWithAI
import { apiBaseUrl, defaultTranslationPromptText, defaultTitleGenerationPromptText, defaultSummaryGenerationPromptText, defaultReferenceParsePromptText } from '../utils/constant.js';
import ApiButton from './ApiButton';
import PromptButton from './PromptButton';
import './ArticleEditPage.css';
import TranslatedChunkDisplay from './TranslatedChunkDisplay';
import ChunkRenderer from './ChunkRenderer';
import ThinkDisplay from './ThinkDisplay';
import TerminologySyncModal from './TerminologySyncModal'; // 导入术语同步模态框
import EnhancedTextArea from './EnhancedTextArea'; // 导入增强的TextArea组件
// import ArticleControls from './ArticleControls'; // Merged into ArticleEditPage
// import ArticleTitle from './ArticleTitle'; // Merged into ArticleEditPage
import { useArticleDataFetching } from '../hooks/useArticleDataFetching.js';
import { useChunkManagement } from '../hooks/useChunkManagement.js';
import { useTranslationControls } from '../hooks/useTranslationControls.js';
import { useEditControls } from '../hooks/useEditControls.js';


// 辅助函数 splitSingleParagraph, splitTextIntoChunks, processInParallelWithMessage 已移至 Sidebar.jsx

const ArticleEditPage = ({ showTranslationOnly = false }) => {
  const paramsForLog = useParams(); // Get params here for logging mount/unmount with uuid

  useEffect(() => {
    return () => {
    };
  }, [paramsForLog.uuid]); // Re-run if uuid changes, effectively logging "remount" or "key change"
  // 添加渲染计数器
  const renderCountRef = useRef(0);
  renderCountRef.current += 1;
  const renderCount = renderCountRef.current;


  const navigate = useNavigate();
  const { message: messageApi, modal } = App.useApp();

  const articleFromStore = useStore(state => state.article);
  // const translated = useStore(state => state.translated || {}); // Not directly used, allChunkTranslatedItems from useChunkManagement is used
  const hasTranslations = useStore(state => Object.keys(state.translated || {}).length > 0);
  const storeApis = useStore(state => state.apis);
  const storeDefaultApiModel = useStore(state => state.defaultApiModel);
  // const storeTranslationPrompt = useStore(state => state.translationPrompt); // Used by translateChunk in store
  const storeParseTextPrompt = useStore(state => state.parseTextPrompt);
  const storeTitleGenerationPrompt = useStore(state => state.titleGenerationPrompt);
  const storeSummaryGenerationPrompt = useStore(state => state.summaryGenerationPrompt);
  const storeReferenceParsePrompt = useStore(state => state.referenceParsePrompt);
  const appendGlossaryToPrompt = useStore(state => state.appendGlossaryToPrompt);
  const user = useStore(state => state.user, shallow); // This user will be used for userChunkSize
  const initialContentProp = useStore(state => state.content); // Get content from store
  const userChunkSize = user?.chunkSize ?? 5; // Derive userChunkSize from store's user
  const currentTranslationControllers = useStore(state => state.currentTranslationControllers);
  const isTranslatingAllActive = useStore(state => state.isTranslatingAllActive);
  const isTranslationGloballyCancelled = useStore(state => state.isTranslationGloballyCancelled);
  // const indicesToTranslate = useStore(state => state.indicesToTranslate); // Managed by translateAll in store
  // const contentFromStore = useStore(state => state.content); // Not directly used, chunked from useChunkManagement is used

  // const logout = useStore(state => state.logout); // Not used in this component
  const setDefaultApiModel = useStore(state => state.setDefaultApiModel);
  // const translateChunk = useStore(state => state.translateChunk); // Used by useTranslationControls
  // const clearAllTranslations = useStore(state => state.clearAllTranslations); // Used by useTranslationControls
  const setUser = useStore(state => state.setUser);
  // const saveChunkSize = useStore(state => state.saveChunkSize); // Used by useChunkManagement
  // const translateAll = useStore(state => state.translateAll); // Used by useTranslationControls
  // const cancelAllTranslations = useStore(state => state.cancelAllTranslations); // Used by useTranslationControls
  // const cancelSingleTranslation = useStore(state => state.cancelSingleTranslation); // Used by useTranslationControls
  const updateOriginalChunk = useStore(state => state.updateOriginalChunk); // Passed to useEditControls
  const updateTranslatedChunk = useStore(state => state.updateTranslatedChunk); // Passed to useEditControls
  const deleteChunkAction = useStore(state => state.deleteChunk);
  // const clearTranslatedChunkAction = useStore(state => state.clearTranslatedChunk); // Used by useTranslationControls
  // const removeTranslationController = useStore(state => state.removeTranslationController); // Internal to store
  // const clearStreamingChunkText = useStore(state => state.clearStreamingChunkText); // Internal to store
  // const setIndicesToTranslate = useStore(state => state.setIndicesToTranslate); // Internal to store
  const saveArticle = useStore(state => state.saveArticle);
  const setContent = useStore(state => state.setContent); // Used by useChunkManagement and full text edit
  // const setArticle = useStore(state => state.setArticle); // Used by useArticleDataFetching
  const isTerminologyModalOpen = useStore(state => state.isTerminologyModalOpen);
  const terminologyListForModal = useStore(state => state.terminologyListForModal);
  const terminologyExtractionProgress = useStore(state => state.terminologyExtractionProgress);
  const startTerminologySyncProcess = useStore(state => state.startTerminologySyncProcess);
  const applyTerminologySync = useStore(state => state.applyTerminologySync);
  const closeTerminologyModal = useStore(state => state.closeTerminologyModal);

  // 合并编辑相关状态
  const [isMergedOriginalEditing, setIsMergedOriginalEditing] = useState(false);
  const [isMergedTranslatedEditing, setIsMergedTranslatedEditing] = useState(false);
  const [mergedOriginalText, setMergedOriginalText] = useState('');
  const [mergedTranslatedText, setMergedTranslatedText] = useState('');

  const params = useParams();
  const { uuid } = params;
  const [concurrencyLevel, setConcurrencyLevel] = useState(user?.concurrencyLevel ?? 3);
  const [articleTitle, setArticleTitle] = useState(null); // 使用 null 表示未加载状态
  const [isGeneratingTitle, setIsGeneratingTitle] = useState(false);
  const [articleSummary, setArticleSummary] = useState(null); // AI总结内容
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false); // AI总结生成状态
  const [isSummaryEditing, setIsSummaryEditing] = useState(false); // 总结编辑状态
  const [editingSummaryText, setEditingSummaryText] = useState(''); // 编辑中的总结文本
  const [streamingSummaryText, setStreamingSummaryText] = useState(''); // 流式输出的总结文本
  const [summaryThinkContent, setSummaryThinkContent] = useState(''); // AI总结的think过程内容
  const [isSummaryThinking, setIsSummaryThinking] = useState(false); // AI总结是否在思考中
  const [summaryThinkCompleted, setSummaryThinkCompleted] = useState(false); // AI总结think过程是否完成
  const [isRefsEditing, setIsRefsEditing] = useState(false); // 参考编辑状态
  const [editingRefsText, setEditingRefsText] = useState(''); // 编辑中的参考文本
  const [isParsingRefs, setIsParsingRefs] = useState(false); // AI解析参考状态
  const [isCitationEditing, setIsCitationEditing] = useState(false); // 来源编辑状态
  const [editingCitationText, setEditingCitationText] = useState(''); // 编辑中的来源文本
  const [refsData, setRefsData] = useState(''); // 存储的参考数据
  const [citationData, setCitationData] = useState(''); // 存储的来源数据
  const debounceTimerRef = useRef(null); // Still needed for cleaning up debouncedSetEditTextHook

  // 工具栏高度相关状态
  const toolbarRef = useRef(null);
  const [toolbarHeight, setToolbarHeight] = useState(80); // 增加默认高度以适应两行

  // State and handlers for Title Editing (migrated from ArticleTitle.jsx)
  const [isTitleEditing, setIsTitleEditing] = useState(false);
  const [editingTitleText, setEditingTitleText] = useState('');
  const {
    chunked,
    allChunkTranslatedItems,
    displayedChunkSize,
    handleDisplayChunkSizeChange,
    handleChunkSizeChangeImmediate,
  } = useChunkManagement(initialContentProp, userChunkSize);

  const locationPathname = window.location.pathname;
  const { isLoading: dataFetchingLoading } = useArticleDataFetching(uuid, locationPathname);

  // 计算标题，现在可以安全地使用 allChunkTranslatedItems 和 chunked
  const computedTitle = useMemo(() => {

    // Ensure uuid is available from params for comparison
    const currentUuidFromParams = params.uuid;

    if (articleFromStore && articleFromStore.uuid === currentUuidFromParams) {
      const currentArticleTitle = articleFromStore.title;
      if (currentArticleTitle && typeof currentArticleTitle === 'string' && currentArticleTitle.trim() !== "") {
        return currentArticleTitle;
      } else {
        const firstTranslatedText = getFirstTranslatedItemText(allChunkTranslatedItems);
        if (!firstTranslatedText && chunked && chunked.length > 0) {
          const firstOriginalText = getAllTextFromChunks([chunked[0]]).substring(0, 100).trim();
          return firstOriginalText || '';
        } else {
          return firstTranslatedText || '';
        }
      }
    } else if (dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams))) {
      return null; // 加载中或等待store同步
    } else {
      return ''; // 无数据
    }
  }, [articleFromStore, allChunkTranslatedItems, chunked, dataFetchingLoading]);

  // 计算总结
  const computedSummary = useMemo(() => {
    const currentUuidFromParams = params.uuid;

    if (articleFromStore && articleFromStore.uuid === currentUuidFromParams) {
      return articleFromStore.summary || null;
    } else if (dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams))) {
      return null; // 加载中或等待store同步
    } else {
      return null; // 无数据
    }
  }, [articleFromStore, dataFetchingLoading]);

  // 计算refs和citation
  const computedRefs = useMemo(() => {
    const currentUuidFromParams = params.uuid;

    if (articleFromStore && articleFromStore.uuid === currentUuidFromParams) {
      return articleFromStore.refs || null;
    } else if (dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams))) {
      return null; // 加载中或等待store同步
    } else {
      return null; // 无数据
    }
  }, [articleFromStore, dataFetchingLoading]);

  const computedCitation = useMemo(() => {
    const currentUuidFromParams = params.uuid;

    if (articleFromStore && articleFromStore.uuid === currentUuidFromParams) {
      return articleFromStore.citation || null;
    } else if (dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams))) {
      return null; // 加载中或等待store同步
    } else {
      return null; // 无数据
    }
  }, [articleFromStore, dataFetchingLoading]);

  // 记录chunked和allChunkTranslatedItems的变化
  const prevChunkedRef = useRef(null);
  const prevAllChunkTranslatedItemsRef = useRef(null);

  useEffect(() => {
    const prevChunked = prevChunkedRef.current;
    const chunkedChanged = prevChunked !== chunked;
    const chunkedLengthChanged = prevChunked?.length !== chunked?.length;


    if (chunkedChanged) {
      prevChunkedRef.current = chunked;
    }
  }, [chunked, renderCount]);

  useEffect(() => {
    const prevItems = prevAllChunkTranslatedItemsRef.current;
    const itemsChanged = prevItems !== allChunkTranslatedItems;
    const itemsLengthChanged = prevItems?.length !== allChunkTranslatedItems?.length;


    if (itemsChanged) {
      prevAllChunkTranslatedItemsRef.current = allChunkTranslatedItems;
    }
  }, [allChunkTranslatedItems, renderCount]);

  // Define handleTitleEdit first as handleSaveTitleEdit depends on it.
  const handleTitleEdit = useCallback(async (newTitle) => {
    const trimmedTitle = typeof newTitle === 'string' ? newTitle.trim() : '';
    let titleToSave = trimmedTitle;
    if (!trimmedTitle) {
      const firstTranslatedItemText = getFirstTranslatedItemText(allChunkTranslatedItems);
      titleToSave = firstTranslatedItemText || '';
    }
    setArticleTitle(titleToSave);
    // setIsTitleEditing(false); // Managed by ArticleTitle component

    const currentArticleFromStore = useStore.getState().article;
    const currentTranslatedFromStore = useStore.getState().translated;
    const currentContentFromStore = useStore.getState().content;

    if (currentArticleFromStore && currentArticleFromStore.uuid) {
      try {
        await saveArticle({
          title: titleToSave,
          translated: currentTranslatedFromStore,
          content: currentContentFromStore
        });
      } catch (error) {
        messageApi.error(`保存标题失败: ${error.message}`);
      }
    }
  }, [allChunkTranslatedItems, saveArticle, messageApi]);

  useEffect(() => {
    if (isTitleEditing) {
      // When editing starts, populate editing text with the current article title
      setEditingTitleText(articleTitle || "");
    }
  }, [articleTitle, isTitleEditing]); // Rerun if articleTitle changes while editing or editing starts

  useEffect(() => {
    if (isSummaryEditing) {
      // When editing starts, populate editing text with the current article summary
      setEditingSummaryText(articleSummary || "");
    }
  }, [articleSummary, isSummaryEditing]); // Rerun if articleSummary changes while editing or editing starts

  const handleSaveTitleEdit = useCallback(() => {
    if (typeof editingTitleText === 'string') {
      handleTitleEdit(editingTitleText.trim()); // handleTitleEdit is now defined above
    }
    setIsTitleEditing(false);
  }, [editingTitleText, handleTitleEdit]);

  const startTitleEdit = useCallback(() => {
    setEditingTitleText(articleTitle || ""); // Initialize with current title
    setIsTitleEditing(true);
  }, [articleTitle]);

  const cancelTitleEdit = useCallback(() => {
    setIsTitleEditing(false);
  }, []);

  const translationContainerRefs = useRef(new Map());

  const stableUpdateTranslationDOM = useCallback((chunkIndex, itemsContent, isActivelyTranslatingChunk) => { // Added isActivelyTranslatingChunk
    const startTime = performance.now();
    const currentItemsToRender = itemsContent || []; // Normalize to empty array if null/undefined

    const container = (typeof window !== 'undefined' && window._translationContainers && window._translationContainers[chunkIndex])
      || translationContainerRefs.current.get(chunkIndex);

    if (!container) {
      return;
    }

    if (typeof window !== 'undefined' && !window._lastTranslationItems) {
      window._lastTranslationItems = {};
    }
    const lastRenderedItems = window._lastTranslationItems ? (window._lastTranslationItems[chunkIndex] || []) : [];

    const areItemsEqual = isEqual(currentItemsToRender, lastRenderedItems);

    if (areItemsEqual) {
      const isEmptyRender = currentItemsToRender.length === 0;
      const containerHasPlaceholder = container.querySelector('[data-placeholder="true"]');
      const containerIsEmptyOrHasOnlyPlaceholder = container.childNodes.length === 0 || (container.childNodes.length === 1 && containerHasPlaceholder);

      if (isEmptyRender && containerIsEmptyOrHasOnlyPlaceholder) {
        return; // Content unchanged (empty, container already placeholder/empty)
      }
      if (!isEmptyRender && !containerIsEmptyOrHasOnlyPlaceholder) { // Both have actual content and content is same
        return; // Content unchanged (non-empty, container has content)
      }
      // If states are mixed (e.g. content same but one is placeholder and other is not, or vice-versa), proceed to normalize DOM.
    }

    // Proceed with DOM update if items are not equal or if container state mismatch
    if (currentItemsToRender.length === 0) {
      const placeholder = document.createElement('div');
      placeholder.style.minHeight = '1.5em';
      placeholder.style.opacity = '0';
      placeholder.innerHTML = '&nbsp;';
      placeholder.setAttribute('data-placeholder', 'true');

      if (container.childNodes.length !== 1 || !container.querySelector('[data-placeholder="true"]')) {
        container.innerHTML = '';
        container.appendChild(placeholder);
      }
    } else {
      const fragment = document.createDocumentFragment();
      currentItemsToRender.forEach((item, idx) => {
        if (item === undefined || item === null) {
          return;
        }

        if (typeof item === 'object' && item !== null) {
          const tag = item.tag || 'p';
          const elem = document.createElement(tag);
          if (item.id) elem.setAttribute('data-item-id', String(item.id));

          if (item.tag === 'img' && item.src) {
            Object.keys(item).forEach(key => {
              if (!['tag', 'id', 'children'].includes(key)) elem.setAttribute(key, item[key]);
            });
            //elem.setAttribute('loading', 'lazy');
          } else if (item.tag === 'table' && typeof item.children === 'string') {
            elem.innerHTML = item.children || ''; // Potentially unsafe
          } else if (item.children) {
            elem.innerHTML = item.children; // 使用innerHTML以正确渲染HTML标签如<sup>、<sub>等
          }
          fragment.appendChild(elem);
        }
      });
      container.innerHTML = '';
      container.appendChild(fragment);
    }

    if (typeof window !== 'undefined') {
      window._lastTranslationItems[chunkIndex] = cloneDeep(currentItemsToRender);
    }

  }, []); // Removed translationContainerRefs from dependencies as it's a stable ref
  const {
    stoppingChunks,
    handleTranslateAll: handleTranslateAllHook,
    handleCancelTranslateAll: handleCancelTranslateAllHook,
    handleClearAll: handleClearAllHook,
    handleTranslateChunk: handleTranslateChunkHook,
    handleStopChunkTranslation: handleStopChunkTranslationHook,
    handleClearTranslatedChunk: handleClearTranslatedChunkHook,
  } = useTranslationControls(chunked, stableUpdateTranslationDOM);

  const {
    editingState,
    editText,
    // isLoading: isAIConverting, // This state is internal to useEditControls
    debouncedSetEditText: debouncedSetEditTextHook,
    handleEditStart: handleEditStartHook,
    handleEditCancel: handleEditCancelHook,
    handleEditSave: handleEditSaveHook,
  } = useEditControls(
    chunked,
    allChunkTranslatedItems,
    updateOriginalChunk, // Pass from store
    updateTranslatedChunk, // Pass from store
    // messageApi and modal are accessed via App.useApp() within the hook
    // storeParseTextPrompt, storeDefaultApiModel, storeApis are accessed via useStore.getState() within the hook
  );

  const treeData = useMemo(() => {
    const systemDefaultApiOption = {
      title: '系统模型',
      value: '系统默认|GLM-4-9B-0414',
      key: '系统默认|GLM-4-9B-0414',
    };
    let userApisData = [];
    if (Array.isArray(storeApis)) {
      userApisData = storeApis.map(api => {
        if (!api || api.key === 'system-default') return null;
        return {
          title: api.provider || '未命名Provider',
          value: api.key,
          key: api.key,
          selectable: false,
          children: Array.isArray(api.models) ? api.models.map(model => {
            const nodeValue = `${api.provider}|${model}`;
            return { title: model, value: nodeValue, key: nodeValue };
          }) : []
        };
      }).filter(Boolean);
    }
    return [systemDefaultApiOption, ...userApisData];
  }, [storeApis]);

  const isChunkTranslating = useMemo(() => (chunkIndex) => {
    return currentTranslationControllers.has(chunkIndex);
  }, [currentTranslationControllers]);

  const shouldDisableTranslateButton = useMemo(() => (chunkIndex) => {
    return currentTranslationControllers.has(chunkIndex);
  }, [currentTranslationControllers]);

  // updateTranslationDOM is now defined before useTranslationControls

  // 同步计算出的标题到本地状态
  useEffect(() => {
    setArticleTitle(computedTitle);
  }, [computedTitle]);

  // 同步计算出的总结到本地状态
  useEffect(() => {
    setArticleSummary(computedSummary);
  }, [computedSummary]);

  // 同步计算出的refs和citation到本地状态
  useEffect(() => {
    setRefsData(computedRefs || '');
  }, [computedRefs]);

  useEffect(() => {
    setCitationData(computedCitation || '');
  }, [computedCitation]);

  // 监听工具栏高度变化
  useEffect(() => {
    const updateToolbarHeight = () => {
      if (toolbarRef.current) {
        const height = toolbarRef.current.offsetHeight;
        console.log('工具栏高度更新:', height);
        setToolbarHeight(Math.max(height + 10, 80)); // 确保最小高度为80px
      }
    };

    // 使用 ResizeObserver 监听工具栏尺寸变化
    let resizeObserver;
    if (toolbarRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          const height = entry.contentRect.height;
          console.log('ResizeObserver 检测到高度变化:', height);
          setToolbarHeight(Math.max(height + 10, 80)); // 确保最小高度为80px
        }
      });
      resizeObserver.observe(toolbarRef.current);
    }

    // 延迟初始测量，确保组件完全渲染
    const timer1 = setTimeout(updateToolbarHeight, 100);
    const timer2 = setTimeout(updateToolbarHeight, 500);
    const timer3 = setTimeout(updateToolbarHeight, 1000);
    const timer4 = setTimeout(updateToolbarHeight, 2000);

    // 监听窗口大小变化
    const handleResize = () => {
      setTimeout(updateToolbarHeight, 100);
    };

    window.addEventListener('resize', handleResize);

    // 监听字体加载完成事件
    if (document.fonts) {
      document.fonts.ready.then(updateToolbarHeight);
    }

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      clearTimeout(timer4);
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, []);

  // 当关键状态变化时重新计算工具栏高度
  useEffect(() => {
    const updateToolbarHeight = () => {
      if (toolbarRef.current) {
        const height = toolbarRef.current.offsetHeight;
        console.log('状态变化触发高度更新:', height);
        setToolbarHeight(Math.max(height + 10, 80));
      }
    };

    setTimeout(updateToolbarHeight, 100);
  }, [concurrencyLevel, displayedChunkSize]);

  useEffect(() => {
    // Effect for concurrencyLevel if any side effects are needed
  }, [concurrencyLevel]);

  useEffect(() => {
    return () => {
      debouncedSetEditTextHook.cancel(); // Cancel from the hook
      if (debounceTimerRef.current) { // This specific ref might not be needed if hook manages its own debounce cleanup
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [debouncedSetEditTextHook]);

  const handlePreview = useCallback(() => {
    if (uuid) {
      const previewUrl = `/article/${uuid}`;
      window.open(previewUrl, '_blank', 'noopener,noreferrer');
    } else {
      messageApi.error("无法获取文章标识符以进行预览");
    }
  }, [uuid, messageApi]);

  const triggerSaveConcurrencyLevel = useCallback(async (value) => {
    const newLevel = value || 1;
    try {
      const response = await fetch(`${apiBaseUrl}api/user/concurrencyLevel`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ concurrencyLevel: newLevel })
      });
      if (!response.ok) throw new Error(`保存并发设置失败: ${response.statusText}`);
      const result = await response.json();
      if (result.success) {
        setUser({ ...user, concurrencyLevel: newLevel });
      } else {
        throw new Error(result.error || '保存并发设置时发生未知错误');
      }
    } catch (error) {
      messageApi.error(`保存并发设置失败: ${error.message}`);
    }
  }, [user, setUser, messageApi]);

  const handleConcurrencyChangeImmediate = useCallback((value) => {
    let newLevel;
    if (typeof value !== 'number') {
      newLevel = concurrencyLevel;
    } else {
      newLevel = (typeof value === 'number' && !isNaN(value)) ? Math.max(1, Math.min(10, value)) : 1;
      setConcurrencyLevel(newLevel);
    }
    triggerSaveConcurrencyLevel(newLevel);
  }, [triggerSaveConcurrencyLevel, concurrencyLevel]);

  const handleModelChange = useCallback((value) => {
    console.log(`[handleModelChange] 开始处理模型更改, value:`, value);
    if (value) {
      const parts = value.split('|');
      let provider, modelName;
      if (parts.length === 2) {
        [provider, modelName] = parts;
        console.log(`[handleModelChange] 解析结果: provider=${provider}, modelName=${modelName}`);
      } else {
        console.warn("API模型选择的value格式不符合预期:", value);
        return;
      }
      if (provider && modelName) {
        console.log(`[handleModelChange] 调用 setDefaultApiModel(${provider}, ${modelName})`);
        setDefaultApiModel(provider, modelName);

        const body = provider === '系统默认' ? { provider: null, model: null } : { provider, model: modelName };
        console.log(`[handleModelChange] 准备发送请求到后端, body:`, body);

        fetch(`${apiBaseUrl}api/user/defaultApiModel`, {
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(body)
        })
        .then(response => {
          console.log(`[handleModelChange] 后端响应状态:`, response.status);
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          console.log(`[handleModelChange] 后端响应数据:`, data);
        })
        .catch(error => {
          console.error(`[handleModelChange] 更新用户默认API模型失败 (${provider}):`, error);
        });
      }
    }
  }, [setDefaultApiModel]);

  const subscribedRef = useRef(false);

  // 不再需要事件计数器

  // 添加一个ref，记录上一次渲染的props和state
  const prevPropsStateRef = useRef({
    chunked: null,
    allChunkTranslatedItems: null,
    editingState: null,
    isTranslatingAllActive: null
  });

  // 检测状态变化并记录日志
  useEffect(() => {
    const prevState = prevPropsStateRef.current;
    const currentState = {
      chunked,
      allChunkTranslatedItems,
      editingState,
      isTranslatingAllActive
    };

    // 检查哪些状态发生了变化
    const changedStates = [];
    if (prevState.chunked !== chunked) changedStates.push('chunked');
    if (prevState.allChunkTranslatedItems !== allChunkTranslatedItems) changedStates.push('allChunkTranslatedItems');
    if (prevState.editingState !== editingState) changedStates.push('editingState');
    if (prevState.isTranslatingAllActive !== isTranslatingAllActive) changedStates.push('isTranslatingAllActive');

    if (changedStates.length > 0) {
    }
    // 更新ref
    prevPropsStateRef.current = currentState;
  }, [chunked, allChunkTranslatedItems, editingState, isTranslatingAllActive, renderCount]);

  // 使用useRef存储最新的状态，避免在事件处理函数中捕获过时的状态
  const latestStateRef = useRef({
    chunked,
    stableUpdateTranslationDOM
  });

  // 每次渲染时更新ref中的状态
  useEffect(() => {
    latestStateRef.current = {
      chunked,
      stableUpdateTranslationDOM
    };
  });

  // 只在组件挂载和卸载时设置事件监听器，避免频繁重新订阅
  useEffect(() => {
    // 创建事件处理函数，使用latestStateRef获取最新状态
    const handleStreamUpdate = (event) => {
      // 从ref中获取最新状态
      const {
        stableUpdateTranslationDOM: currentUpdateDOM
      } = latestStateRef.current;

      const { chunkIndex, text } = event.detail;

      if (chunkIndex !== undefined) {
        const containerExists = window._translationContainers && window._translationContainers[chunkIndex];
        if (containerExists) {
          // Direct DOM update from stream event is disabled.
          // Updates are driven by Zustand store changes via TranslatedChunkDisplay's useEffect.
        }
      }
    };

    // 只在组件挂载时添加事件监听器
    if (!subscribedRef.current) {
      window.addEventListener('stream-translation-update', handleStreamUpdate);
      subscribedRef.current = true;
    }

    // 在组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('stream-translation-update', handleStreamUpdate);
      subscribedRef.current = false;
    };
  }, []); // 空依赖数组，只在挂载和卸载时执行

  const renderTranslatedContent = useCallback((items, translatedItems, chunkIndex) => {
    const isEditingTranslated = editingState.type === 'translated' && editingState.index === chunkIndex;
    const isTranslating = isChunkTranslating(chunkIndex);
    const hasMismatchedTagCount = checkTagCountMismatch(translatedItems, items, isTranslating);

    return (
      <>
        {/* Text Area for Editing - conditionally rendered inside a visibility-toggled div */}
        <div style={{ display: isEditingTranslated ? 'block' : 'none', width: '100%', height: '100%' }}>
          {isEditingTranslated && (
            <EnhancedTextArea
              key={`edit-area-${chunkIndex}`} // Re-key to ensure defaultValue applies on edit start
              defaultValue={editText} // editText is specific to the current editing chunk
              onChange={debouncedSetEditTextHook}
              autoSize={{ minRows: 6, maxRows: 25 }}
              style={{ width: '100%', resize: 'vertical' }}
              autoFocus
            />
          )}
        </div>

        {/* TranslatedChunkDisplay for Viewing - visibility controlled by style, always rendered */}
        <div style={{ display: !isEditingTranslated ? 'block' : 'none', width: '100%', height: '100%' }}>
          {hasMismatchedTagCount && (
            <div className="tag-count-mismatch-warning">
              <span style={{ backgroundColor: '#F5A623', color: 'white', borderRadius: '50%', width: '16px', height: '16px', display: 'inline-flex', justifyContent: 'center', alignItems: 'center', marginRight: '8px', fontSize: '12px', fontWeight: 'bold' }}>!</span>
              原文与译文段数不一致
            </div>
          )}
          <TranslatedChunkDisplay
            key={`display-chunk-${chunkIndex}`} // Stable key for the display component
            chunkIndex={chunkIndex}
            translatedItems={translatedItems}
            isEditingTranslatedFlag={false} // Always false as edit is handled by the TextArea above
            updateDOMFunc={stableUpdateTranslationDOM}
            containerRefs={translationContainerRefs}
            isActivelyTranslating={isTranslating} // Pass down the isTranslating state
          />
        </div>
      </>
    );
  }, [editingState, editText, debouncedSetEditTextHook, isChunkTranslating, stableUpdateTranslationDOM, translationContainerRefs, checkTagCountMismatch]);

  const handleDeleteChunk = useCallback(async (idx) => {
    try {
      await deleteChunkAction(chunked[idx]); // Assumes chunked[idx] is the correct identifier for deletion
      messageApi.success(`区块 ${idx} 已删除`);
    } catch (error) {
      messageApi.error(`删除区块失败: ${error.message}`);
    }
  }, [chunked, deleteChunkAction, messageApi]);

  const handleGenerateSummary = async () => { // Removed isViaProxy parameter
    if (!articleFromStore || !articleFromStore.uuid) {
      messageApi.error("文章信息不完整，无法生成总结。");
      return;
    }
    if (isGeneratingSummary) return; // Prevent multiple calls

    const allText = getAllTextFromChunks(chunked);
    if (!allText.trim()) {
      messageApi.info("文章内容为空，无法生成总结。");
      return;
    }
    setIsGeneratingSummary(true);
    setStreamingSummaryText('');
    setSummaryThinkContent('');
    setIsSummaryThinking(false);
    setSummaryThinkCompleted(false);
    const aiMessageKey = `ai-generating-summary`; // Simplified key

    try {
      let providerName, modelName, selectedApiConfig;
      if (storeDefaultApiModel && storeDefaultApiModel[0] && storeDefaultApiModel[1]) {
        [providerName, modelName] = storeDefaultApiModel;
        if (providerName === '系统默认') {
          selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
          if (!selectedApiConfig.models.includes(modelName)) modelName = selectedApiConfig.models[0];
        } else {
          selectedApiConfig = storeApis.find(api => api.provider === providerName);
        }
      } else {
        providerName = '系统默认'; modelName = 'GLM-4-9B-0414';
        selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
      }

      if (!selectedApiConfig || (selectedApiConfig.key !== 'system-default' && (!selectedApiConfig.apiUrl || !selectedApiConfig.apiKey)) || (selectedApiConfig.key === 'system-default' && !selectedApiConfig.apiUrl) || !selectedApiConfig.models || !selectedApiConfig.models.includes(modelName)) {
        throw new Error(`AI模型配置不完整或无效: ${providerName} - ${modelName}`);
      }

      const baseSummaryPrompt = storeSummaryGenerationPrompt || defaultSummaryGenerationPromptText;
      const summarySystemPrompt = appendGlossaryToPrompt(baseSummaryPrompt, 'summaryGeneration');

      let finalGeneratedSummary = '';
      let currentAccumulatedContent = '';
      let currentAccumulatedReasoning = '';
      let currentHasStartedFormalContent = false;
      let summaryThinkUpdateTimeout = null;

      const batchSummaryThinkUpdate = (content, thinking, completed) => {
        if (summaryThinkUpdateTimeout) clearTimeout(summaryThinkUpdateTimeout);
        summaryThinkUpdateTimeout = setTimeout(() => {
          if (content !== undefined) setSummaryThinkContent(content);
          if (thinking !== undefined) setIsSummaryThinking(thinking);
          if (completed !== undefined) setSummaryThinkCompleted(completed);
        }, 50);
      };
      
      console.log(`[Summary Refactored] Model: ${modelName}`); // Removed isThinkModelUsed from log

      const handleStreamChunk = (chunkData) => {
        const { content, reasoning, error, isFinal } = chunkData;

        if (error) {
          console.error("[handleGenerateSummary onChunk] Error reported in chunk:", error);
          return false;
        }

        if (isFinal) {
          // Always attempt to finalize think update, regardless of isThinkModelUsed
          if (!currentHasStartedFormalContent) { // If formal content never started, ensure think process is marked completed
            batchSummaryThinkUpdate(undefined, false, true);
          }
          if (summaryThinkUpdateTimeout) clearTimeout(summaryThinkUpdateTimeout);
          return;
        }

        if (reasoning) { // Always process reasoning if present
          currentAccumulatedReasoning += reasoning;
          batchSummaryThinkUpdate(currentAccumulatedReasoning, true, undefined); // Update think display
        }

        if (content) {
          currentAccumulatedContent += content;
          // 使用增强的parseThinkContent函数，同时处理<think>标签和reasoning格式
          const parsed = parseThinkContent(currentAccumulatedContent, currentAccumulatedReasoning);

          // 优先显示<think>标签内容，如果没有则显示reasoning内容
          if (parsed.thinkBlocks.length > 0) {
            const latestThink = parsed.thinkBlocks[parsed.thinkBlocks.length - 1];
            if (latestThink.type === 'think-tag') {
              batchSummaryThinkUpdate(latestThink.content, true, undefined); // Update with <think> tag content
            } else if (latestThink.type === 'reasoning' && !parsed.thinkBlocks.some(block => block.type === 'think-tag')) {
              // 只有在没有<think>标签时才显示reasoning内容
              batchSummaryThinkUpdate(latestThink.content, true, undefined);
            }
          }

          finalGeneratedSummary = parsed.cleanContent; // Use cleaned content for summary

          // If formal content (non-empty clean content) starts, mark think as completed
          if (finalGeneratedSummary.trim() && !currentHasStartedFormalContent) {
            currentHasStartedFormalContent = true;
            batchSummaryThinkUpdate(undefined, false, true);
          }
          setStreamingSummaryText(finalGeneratedSummary);
        }
        return true;
      };

      const streamResult = await streamTextWithAI(
        allText,
        summarySystemPrompt,
        selectedApiConfig,
        modelName,
        null,
        handleStreamChunk,
        false,
        { temperature: 0.7 }
      );

      if (!streamResult.success) {
        throw new Error(streamResult.error || 'AI 生成总结流式处理失败 (via streamTextWithAI)');
      }
      
      if (summaryThinkUpdateTimeout) clearTimeout(summaryThinkUpdateTimeout);
      // Always attempt to finalize think update if formal content never started
      if (!currentHasStartedFormalContent) {
          batchSummaryThinkUpdate(undefined, false, true);
      }
      // Ensure thinking state is finalized after stream completion
      setIsSummaryThinking(false);
      setSummaryThinkCompleted(true);

      if (finalGeneratedSummary.trim()) {
        await handleSummaryEdit(finalGeneratedSummary.trim());
        messageApi.success({ content: 'AI已生成新总结并保存', key: aiMessageKey, duration: 3 });
      } else {
        throw new Error('AI返回的总结为空或格式不正确 (after stream)');
      }

    } catch (error) {
      console.error("Error generating summary with AI (Refactored):", error);
      messageApi.error({ content: `AI生成总结失败: ${error.message || '未知错误'}`, key: aiMessageKey, duration: 5 });
      // Always ensure thinking state is finalized on error
      setIsSummaryThinking(false);
      setSummaryThinkCompleted(true);
    } finally {
      setIsGeneratingSummary(false);
      // setStreamingSummaryText(''); // Optional: clear streaming text on finish/error
    }
  };

  const handleGenerateTitle = async () => { // Removed isViaProxy parameter
    if (!articleFromStore || !articleFromStore.uuid) {
      messageApi.error("文章信息不完整，无法生成标题。");
      return;
    }
    if (isGeneratingTitle) return; // Prevent multiple calls

    const allText = getAllTextFromChunks(chunked);
    if (!allText.trim()) {
      messageApi.info("文章内容为空，无法生成标题。");
      return;
    }
    setIsGeneratingTitle(true);
    const aiMessageKey = `ai-generating-title`; // Simplified key

    try {
      let providerName, modelName, selectedApiConfig;
      if (storeDefaultApiModel && storeDefaultApiModel[0] && storeDefaultApiModel[1]) {
        [providerName, modelName] = storeDefaultApiModel;
        if (providerName === '系统默认') {
          selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
          if (!selectedApiConfig.models.includes(modelName)) modelName = selectedApiConfig.models[0];
        } else {
          selectedApiConfig = storeApis.find(api => api.provider === providerName);
        }
      } else {
        providerName = '系统默认'; modelName = 'GLM-4-9B-0414';
        selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
      }

      if (!selectedApiConfig || (selectedApiConfig.key !== 'system-default' && (!selectedApiConfig.apiUrl || !selectedApiConfig.apiKey)) || (selectedApiConfig.key === 'system-default' && !selectedApiConfig.apiUrl) || !selectedApiConfig.models || !selectedApiConfig.models.includes(modelName)) {
        throw new Error(`AI模型配置不完整或无效: ${providerName} - ${modelName}`);
      }

      const baseTitlePrompt = storeTitleGenerationPrompt || defaultTitleGenerationPromptText;
      const titleSystemPrompt = appendGlossaryToPrompt(baseTitlePrompt, 'titleGeneration');
      const userPromptTemplate = "\${text}"; // Text itself will be the user message content

      const aiResult = await formatTextWithAI(
        allText,
        titleSystemPrompt,
        selectedApiConfig,
        modelName,
        null, // signal - AbortSignal can be added if cancellation is needed
        1,    // concurrencyLevel - use default of 1 as chunking is disabled
        true  // disableChunking - set to true for title generation
      );

      if (aiResult.error) {
        throw new Error(aiResult.error); // Throw the specific error from AI utility
      }

      const generatedTitle = aiResult.formattedText?.trim();

      if (generatedTitle) {
        await handleTitleEdit(generatedTitle);
        messageApi.success({ content: 'AI已生成新标题并保存', key: aiMessageKey, duration: 3 });
      } else {
        throw new Error('AI返回的标题为空或格式不正确 (via formatTextWithAI)');
      }
    } catch (error) {
      console.error("Error generating title with AI:", error);
      messageApi.error({ content: `AI生成标题失败: ${error.message || '未知错误'}`, key: aiMessageKey, duration: 5 });
    } finally {
      setIsGeneratingTitle(false);
    }
  };

  // 总结编辑相关函数
  const handleSummaryEdit = useCallback(async (newSummary) => {
    const trimmedSummary = typeof newSummary === 'string' ? newSummary.trim() : '';
    setArticleSummary(trimmedSummary || null);

    const currentArticleFromStore = useStore.getState().article;
    const currentTranslatedFromStore = useStore.getState().translated;
    const currentContentFromStore = useStore.getState().content;

    if (currentArticleFromStore && currentArticleFromStore.uuid) {
      try {
        await saveArticle({
          summary: trimmedSummary || null,
          translated: currentTranslatedFromStore,
          content: currentContentFromStore
        });
      } catch (error) {
        messageApi.error(`保存总结失败: ${error.message}`);
      }
    }
  }, [saveArticle, messageApi]);

  const handleSaveSummaryEdit = useCallback(() => {
    if (typeof editingSummaryText === 'string') {
      handleSummaryEdit(editingSummaryText.trim());
    }
    setIsSummaryEditing(false);
  }, [editingSummaryText, handleSummaryEdit]);

  const startSummaryEdit = useCallback(() => {
    setEditingSummaryText(articleSummary || "");
    setIsSummaryEditing(true);
  }, [articleSummary]);

  const cancelSummaryEdit = useCallback(() => {
    setEditingSummaryText(articleSummary || ""); // 恢复原始内容
    setIsSummaryEditing(false);
  }, [articleSummary]);

  const handleClearSummary = useCallback(async () => {
    await handleSummaryEdit(null);
    messageApi.success('总结已清空');
  }, [handleSummaryEdit, messageApi]);

  // 参考相关函数
  const startRefsEdit = useCallback(() => {
    setEditingRefsText(refsData || ''); // 初始化为存储的数据
    setIsRefsEditing(true);
  }, [refsData]);

  const cancelRefsEdit = useCallback(() => {
    setIsRefsEditing(false);
    setEditingRefsText('');
  }, []);

  const handleSaveRefs = useCallback(async () => {
    try {
      const response = await fetch(`/api/article/${uuid}/refs`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refs: editingRefsText }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`保存参考失败: ${errorData.error || response.statusText}`);
      }

      setIsRefsEditing(false);
      setRefsData(editingRefsText); // 更新本地存储的数据

      // 更新store中的article数据
      const currentArticle = useStore.getState().article;
      if (currentArticle) {
        useStore.setState({
          article: {
            ...currentArticle,
            refs: editingRefsText
          }
        });
      }

      messageApi.success('参考已保存');
    } catch (error) {
      console.error('保存参考失败:', error);
      messageApi.error(`保存参考失败: ${error.message || '未知错误'}`);
    }
  }, [uuid, editingRefsText, messageApi]);

  const handleParseRefs = useCallback(async () => { // isViaProxy parameter removed
    if (!editingRefsText.trim()) {
      messageApi.warning('请先输入参考内容');
      return;
    }

    if (isParsingRefs) return; // Simpler check as formatTextWithAI handles proxy internally

    setIsParsingRefs(true);
    const aiMessageKey = `ai-parsing-reference`; // Simplified key

    try {
      messageApi.loading({ content: 'AI正在解析参考文献...', key: aiMessageKey, duration: 0 });

      let selectedApiConfig;
      let modelName;

      if (storeDefaultApiModel && storeDefaultApiModel[0] && storeDefaultApiModel[1]) {
        const [provider, model] = storeDefaultApiModel;
        if (provider === '系统默认') {
          selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: 'INTERNAL_FRONTEND_CALL', models: [model] };
          modelName = model;
          if (selectedApiConfig.models && !selectedApiConfig.models.includes(modelName) && selectedApiConfig.models.length > 0) {
            modelName = selectedApiConfig.models[0];
          } else if (selectedApiConfig.models && selectedApiConfig.models.length === 0) {
            modelName = 'GLM-4-9B-0414'; 
            selectedApiConfig.models = [modelName];
          }
        } else {
          selectedApiConfig = storeApis.find(api => api.provider === provider);
          modelName = model;
        }
      } else { 
          selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: 'INTERNAL_FRONTEND_CALL', models: ['GLM-4-9B-0414'] };
          modelName = 'GLM-4-9B-0414';
      }

      if (!selectedApiConfig || !modelName || !selectedApiConfig.models || !selectedApiConfig.models.includes(modelName)) {
        console.error('Invalid API config or modelName:', { provider: selectedApiConfig?.provider, modelName, availableModels: selectedApiConfig?.models });
        throw new Error(`AI模型配置不完整或无效: ${selectedApiConfig?.provider || '未知Provider'} - ${modelName || '未知Model'}`);
      }

      const systemPrompt = storeReferenceParsePrompt || defaultReferenceParsePromptText;
      const userPromptTemplateForRefs = "\${text}"; // Standard user prompt template

      // Call the shared utility function formatTextWithAI
      const aiResult = await formatTextWithAI(
        editingRefsText,
        systemPrompt,
        selectedApiConfig,
        modelName,
        null // signal - AbortSignal can be added if cancellation is needed
        // userPromptTemplateForRefs is not directly passed; text itself is the user content.
        // isViaProxyInternal and options are not params of the current formatTextWithAI signature.
      );

      if (aiResult.error) {
        throw new Error(aiResult.error); // Throw the specific error from AI utility
      }

      const formattedResultText = aiResult.formattedText; // Use formattedText

      if (formattedResultText) {
        setEditingRefsText(formattedResultText);
        messageApi.success({ content: 'AI已完成参考解析', key: aiMessageKey, duration: 3 });
      } else {
        throw new Error('AI返回的解析结果为空或格式不正确 (via formatTextWithAI)');
      }
    } catch (error) {
      console.error("Error parsing reference with AI:", error);
      messageApi.error({ content: `AI解析参考失败: ${error.message || '未知错误'}`, key: aiMessageKey, duration: 5 });
    } finally {
      setIsParsingRefs(false);
    }
  }, [editingRefsText, isParsingRefs, storeReferenceParsePrompt, messageApi, defaultReferenceParsePromptText, storeApis, storeDefaultApiModel]);

  // 来源相关函数
  const startCitationEdit = useCallback(() => {
    setEditingCitationText(citationData || ''); // 初始化为存储的数据
    setIsCitationEditing(true);
  }, [citationData]);

  const cancelCitationEdit = useCallback(() => {
    setIsCitationEditing(false);
    setEditingCitationText('');
  }, []);

  const handleSaveCitation = useCallback(async () => {
    try {
      const response = await fetch(`/api/article/${uuid}/citation`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ citation: editingCitationText }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`保存来源失败: ${errorData.error || response.statusText}`);
      }

      setIsCitationEditing(false);
      setCitationData(editingCitationText); // 更新本地存储的数据

      // 更新store中的article数据
      const currentArticle = useStore.getState().article;
      if (currentArticle) {
        useStore.setState({
          article: {
            ...currentArticle,
            citation: editingCitationText
          }
        });
      }

      messageApi.success('来源已保存');
    } catch (error) {
      console.error('保存来源失败:', error);
      messageApi.error(`保存来源失败: ${error.message || '未知错误'}`);
    }
  }, [uuid, editingCitationText, messageApi]);

  const handleClearCitation = useCallback(async () => {
    try {
      const response = await fetch(`/api/article/${uuid}/citation`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ citation: null }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`清空来源失败: ${errorData.error || response.statusText}`);
      }

      setCitationData(''); // 更新本地存储的数据

      // 更新store中的article数据
      const currentArticle = useStore.getState().article;
      if (currentArticle) {
        useStore.setState({
          article: {
            ...currentArticle,
            citation: null
          }
        });
      }

      messageApi.success('来源已清空');
    } catch (error) {
      console.error('清空来源失败:', error);
      messageApi.error(`清空来源失败: ${error.message || '未知错误'}`);
    }
  }, [uuid, messageApi]);

  const handleClearRefs = useCallback(async () => {
    try {
      const response = await fetch(`/api/article/${uuid}/refs`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refs: null }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`清空参考失败: ${errorData.error || response.statusText}`);
      }

      setRefsData(''); // 更新本地存储的数据

      // 更新store中的article数据
      const currentArticle = useStore.getState().article;
      if (currentArticle) {
        useStore.setState({
          article: {
            ...currentArticle,
            refs: null
          }
        });
      }

      messageApi.success('参考已清空');
    } catch (error) {
      console.error('清空参考失败:', error);
      messageApi.error(`清空参考失败: ${error.message || '未知错误'}`);
    }
  }, [uuid, messageApi]);

  // 格式转换辅助函数（与useEditControls中的逻辑保持一致）
  const containsHtmlTags = (text) => {
    return /<[^>]+>/g.test(text);
  };

  const convertPlainTextToHtml = (text) => {
    if (!text || text.trim() === '') return '';

    // Split by line breaks and filter out empty lines
    const lines = text.split(/\r?\n/).filter(line => line.trim() !== '');

    // Wrap each line in <p> tags, join without newlines to avoid root text nodes
    const result = lines.map(line => `<p>${line.trim()}</p>`).join('');
    console.log('[convertPlainTextToHtml] 输入:', JSON.stringify(text));
    console.log('[convertPlainTextToHtml] 输出:', JSON.stringify(result));
    return result;
  };

  // 全文编辑相关函数
  const getFullOriginalText = useCallback(() => {
    if (!chunked || chunked.length === 0) return '';

    return chunked.map(chunk => {
      if (!chunk || !Array.isArray(chunk)) return '';
      return chunk.map(item => {
        if (!item || typeof item !== 'object') return '';
        if (item.tag === 'img') {
          return `<img src="${item.src}" alt="${item.alt || ''}" />`;
        } else if (item.tag === 'table' && item.children) {
          return `<table>${item.children}</table>`;
        } else if (item.children) {
          return `<${item.tag}>${item.children}</${item.tag}>`;
        } else {
          return `<${item.tag} />`;
        }
      }).filter(Boolean).join('\n');
    }).filter(Boolean).join('\n');
  }, [chunked]);

  const getFullTranslatedText = useCallback(() => {
    if (!allChunkTranslatedItems || allChunkTranslatedItems.length === 0) return '';

    return allChunkTranslatedItems.map(chunkItems => {
      if (!chunkItems || !Array.isArray(chunkItems) || chunkItems.length === 0) return '';

      return chunkItems.map(item => {
        if (!item || typeof item !== 'object') return '';
        if (item.tag === 'img') {
          return `<img src="${item.src}" alt="${item.alt || ''}" />`;
        } else if (item.tag === 'table' && item.children) {
          return `<table>${item.children}</table>`;
        } else if (item.children) {
          return `<${item.tag}>${item.children}</${item.tag}>`;
        } else {
          return `<${item.tag} />`;
        }
      }).filter(Boolean).join('\n');
    }).filter(Boolean).join('\n');
  }, [allChunkTranslatedItems]);

  // 原文合并编辑相关函数
  const handleStartMergedOriginalEdit = useCallback(() => {
    const mergedText = getFullOriginalText();
    setMergedOriginalText(mergedText);
    setIsMergedOriginalEditing(true);
  }, [getFullOriginalText]);

  const handleCancelMergedOriginalEdit = useCallback(() => {
    setIsMergedOriginalEditing(false);
    setMergedOriginalText('');
  }, []);

  const handleSaveMergedOriginalEdit = useCallback(async () => {
    try {
      let processedText = mergedOriginalText;

      // 如果输入是纯文本（无HTML标签），自动转换为HTML格式
      if (mergedOriginalText.trim() !== '' && !containsHtmlTags(mergedOriginalText.trim())) {
        processedText = convertPlainTextToHtml(mergedOriginalText);
        console.log('[handleSaveMergedOriginalEdit] 检测到纯文本输入，已自动添加<p>标签:', processedText);
      }

      console.log('[handleSaveMergedOriginalEdit] 处理后的文本:', processedText);
      console.log('[handleSaveMergedOriginalEdit] 调用html2content前...');
      const parsedOriginal = html2content(processedText);
      console.log('[handleSaveMergedOriginalEdit] html2content返回结果:', parsedOriginal);

      if (parsedOriginal && parsedOriginal.length > 0) {
        setContent(parsedOriginal);

        // 保存到后端数据库
        const currentArticleFromStore = useStore.getState().article;
        const currentTranslatedFromStore = useStore.getState().translated;

        if (currentArticleFromStore && currentArticleFromStore.uuid) {
          await saveArticle({
            content: parsedOriginal,
            translated: currentTranslatedFromStore
          });
          messageApi.success('原文合并编辑已保存');
          setIsMergedOriginalEditing(false);
          setMergedOriginalText('');
        } else {
          messageApi.error('无法保存：缺少文章信息');
        }
      } else {
        messageApi.warning('解析原文内容失败，请检查格式');
      }
    } catch (error) {
      console.error('保存原文合并编辑失败:', error);
      messageApi.error(`保存失败: ${error.message || '未知错误'}`);
    }
  }, [mergedOriginalText, setContent, saveArticle, messageApi, containsHtmlTags, convertPlainTextToHtml]);

  // 译文合并编辑相关函数
  const handleStartMergedTranslatedEdit = useCallback(() => {
    const mergedText = getFullTranslatedText();
    setMergedTranslatedText(mergedText);
    setIsMergedTranslatedEditing(true);
  }, [getFullTranslatedText]);

  const handleCancelMergedTranslatedEdit = useCallback(() => {
    setIsMergedTranslatedEditing(false);
    setMergedTranslatedText('');
  }, []);

  const handleSaveMergedTranslatedEdit = useCallback(async () => {
    try {
      console.log('[handleSaveMergedTranslatedEdit] 原始输入文本:', JSON.stringify(mergedTranslatedText));
      console.log('[handleSaveMergedTranslatedEdit] 原始输入文本长度:', mergedTranslatedText.length);
      console.log('[handleSaveMergedTranslatedEdit] 是否包含HTML标签:', containsHtmlTags(mergedTranslatedText.trim()));

      // 直接测试简单的HTML
      console.log('[测试] 直接测试html2content("<p>test</p>"):', html2content('<p>test</p>'));
      console.log('[测试] 直接测试html2content("<p>dddd</p>"):', html2content('<p>dddd</p>'));

      let processedText = mergedTranslatedText;

      // 如果输入是纯文本（无HTML标签），自动转换为HTML格式
      if (mergedTranslatedText.trim() !== '' && !containsHtmlTags(mergedTranslatedText.trim())) {
        processedText = convertPlainTextToHtml(mergedTranslatedText);
        console.log('[handleSaveMergedTranslatedEdit] 检测到纯文本输入，已自动添加<p>标签:', processedText);
      }

      console.log('[handleSaveMergedTranslatedEdit] 处理后的文本:', JSON.stringify(processedText));
      console.log('[handleSaveMergedTranslatedEdit] 调用html2content前...');

      let parsedTranslated;
      let conversionOk = true;

      try {
        parsedTranslated = html2content(processedText);
        console.log('[handleSaveMergedTranslatedEdit] html2content返回结果:', parsedTranslated);

        if (processedText.trim() !== '' && (!parsedTranslated || parsedTranslated.length === 0)) {
          conversionOk = false;
          console.log('[handleSaveMergedTranslatedEdit] 转换失败：返回空数组');
        } else if (Array.isArray(parsedTranslated) && parsedTranslated.length > 0 && !parsedTranslated.every(item => item && typeof item.tag === 'string')) {
          conversionOk = false;
          console.log('[handleSaveMergedTranslatedEdit] 转换失败：数组项格式不正确');
        }
        if (parsedTranslated === null || typeof parsedTranslated === 'undefined') {
          conversionOk = false;
          console.log('[handleSaveMergedTranslatedEdit] 转换失败：返回null或undefined');
        }
      } catch (e) {
        console.error("[handleSaveMergedTranslatedEdit] html2content转换错误:", e);
        conversionOk = false;
      }

      if (conversionOk && parsedTranslated && parsedTranslated.length > 0) {
        // 将解析后的译文按照当前分块结构重新分配
        const newTranslatedItems = [];
        let currentIndex = 0;

        for (let i = 0; i < chunked.length; i++) {
          const chunkSize = chunked[i].length;
          const chunkTranslated = parsedTranslated.slice(currentIndex, currentIndex + chunkSize);
          newTranslatedItems.push(chunkTranslated);
          currentIndex += chunkSize;
        }

        // 更新store中的翻译内容
        newTranslatedItems.forEach((items, chunkIndex) => {
          if (items.length > 0) {
            updateTranslatedChunk(chunkIndex, items);
          }
        });

        // 保存到后端数据库
        const currentArticleFromStore = useStore.getState().article;
        const currentTranslatedFromStore = useStore.getState().translated;
        const currentContentFromStore = useStore.getState().content;

        if (currentArticleFromStore && currentArticleFromStore.uuid) {
          await saveArticle({
            content: currentContentFromStore,
            translated: currentTranslatedFromStore
          });
          messageApi.success('译文合并编辑已保存');
          setIsMergedTranslatedEditing(false);
          setMergedTranslatedText('');
        } else {
          messageApi.error('无法保存：缺少文章信息');
        }
      } else {
        // 转换失败，提供详细的错误信息
        console.log('[handleSaveMergedTranslatedEdit] 格式转换失败，原始文本:', JSON.stringify(mergedTranslatedText));
        console.log('[handleSaveMergedTranslatedEdit] 处理后文本:', JSON.stringify(processedText));
        messageApi.warning('解析译文内容失败，请检查格式。提示：请确保内容为有效的HTML格式，或输入纯文本让系统自动转换。');
      }
    } catch (error) {
      console.error('保存译文合并编辑失败:', error);
      messageApi.error(`保存失败: ${error.message || '未知错误'}`);
    }
  }, [mergedTranslatedText, chunked, updateTranslatedChunk, saveArticle, messageApi, containsHtmlTags, convertPlainTextToHtml]);



  // handleTitleEdit has been moved above handleSaveTitleEdit to resolve initialization order issue.

  // Determine true loading state considering store sync
  const currentUuidFromParams = params.uuid; // Already defined as 'uuid' earlier, but re-aliasing for clarity here
  const isTrulyLoading = dataFetchingLoading || (currentUuidFromParams && (!articleFromStore || articleFromStore.uuid !== currentUuidFromParams));
  if (isTrulyLoading && !dataFetchingLoading && currentUuidFromParams && articleFromStore?.uuid !== currentUuidFromParams) {
  }


  return (
    <div className="article-edit-container" style={{padding: '0 24px 24px'}}>
      {isTrulyLoading ? (
        <div style={{ textAlign: 'center', margin: '50px' }}>加载中...</div>
      ) : (
        <div style={{ marginTop: `${toolbarHeight}px` }}>
          {/* ArticleControls JSX merged in */}
          <Row
            ref={toolbarRef}
            className="article-controls"
            justify="space-between"
            align="middle" // Added for vertical alignment
            style={{
              position: 'fixed',
              top: 0,
              left: '250px', // Adjusted to match Sidebar width
              right: 0,
              zIndex: 1000,
              backgroundColor: '#f5f5f5', // Match Sidebar background color
              padding: '16px 24px 6px 24px', // Reduced vertical padding
              borderBottom: '1px solid #e0e0e0' // Adjusted border color for new bg, slightly lighter than previous #e8e8e8 but distinct from #f5f5f5
            }}
          >
            <Col>
              <Space className="controls-section">
                <ApiButton />
                <PromptButton />
                <InputNumber
                  min={1}
                  value={displayedChunkSize}
                  onChange={handleDisplayChunkSizeChange}
                  onBlur={handleChunkSizeChangeImmediate}
                  onPressEnter={handleChunkSizeChangeImmediate}
                  style={{ width: '120px' }}
                  addonBefore="分块数"
                  controls={true}
                  size="small"
                  className="custom-small-font"
                />
                <TreeSelect
                  style={{ width: 100 }}
                  dropdownStyle={{ maxHeight: 400, overflow: 'auto', width: 300 }}
                  treeData={treeData}
                  placeholder="选择API 模型"
                  treeDefaultExpandAll
                  value={
                    storeDefaultApiModel && storeDefaultApiModel[0] && storeDefaultApiModel[1]
                      ? `${storeDefaultApiModel[0]}|${storeDefaultApiModel[1]}`
                      : '系统默认|GLM-4-9B-0414'
                  }
                  onChange={handleModelChange}
                  treeNodeLabelProp="title"
                  size="small"
                  className="custom-small-font"
                />
                <InputNumber
                  min={1}
                  max={10}
                  value={concurrencyLevel}
                  onChange={handleConcurrencyChangeImmediate}
                  onBlur={handleConcurrencyChangeImmediate}
                  onPressEnter={handleConcurrencyChangeImmediate}
                  addonBefore="并发数"
                  style={{ width: '120px' }}
                  controls={true}
                  size="small"
                  className="custom-small-font"
                />
                {isTranslatingAllActive ? (
                  <Button
                    type="primary"
                    danger
                    onClick={handleCancelTranslateAllHook}
                    disabled={isTranslationGloballyCancelled}
                    loading={isTranslationGloballyCancelled}
                    size="small"
                    className="custom-small-font"
                  >
                    停止翻译
                  </Button>
                ) : (
                  <Button
                    type="primary"
                    onClick={handleTranslateAllHook}
                    disabled={isTranslatingAllActive || isTranslationGloballyCancelled}
                    size="small"
                    className="custom-small-font"
                  >
                    翻译全文
                  </Button>
                )}
                <Button
                  onClick={() => startTerminologySyncProcess(chunked)}
                  disabled={!hasTranslations || isTranslatingAllActive} // 仅在有翻译内容且非全文翻译中时可用
                  size="small"
                  className="custom-small-font"
                >
                  术语统一
                </Button>

              </Space>
            </Col>

            <Col>
              <Space className="controls-section">
                {uuid && (
                  <Button type="default" onClick={handlePreview} size="small" className="custom-small-font">
                    预览译文
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
          {/* ArticleTitle JSX merged in */}
          <Row style={{ margin: '10px 0', alignItems: 'center' }} gutter={8}>
            <Col flex="auto">
              {isTitleEditing ? (
                <Space align="baseline" size={4}>
                  <Input
                    value={editingTitleText}
                    onChange={(e) => setEditingTitleText(e.target.value)}
                    onPressEnter={handleSaveTitleEdit}
                    onBlur={handleSaveTitleEdit} // Save on blur
                    style={{ fontSize: '1.2em', flexGrow: 1, minWidth: '300px' }}
                    placeholder="请输入标题"
                    autoFocus
                  />
                  <Button
                    type="text"
                    className="icon-title-button custom-small-font"
                    icon={<CloseOutlined style={{ fontSize: '14px' }} />}
                    onClick={cancelTitleEdit}
                    size="small"
                    style={{ color: '#595959' }}
                    title="取消编辑"
                  />
                </Space>
              ) : (
                <Space align="baseline" size={0}>
                  <Typography.Paragraph
                    style={{
                      fontSize: '1.2em',
                      marginBottom: '0',
                      marginRight: '8px',
                      flexGrow: 1,
                    }}
                  >
                    {articleTitle === null ? (
                      <span style={{ color: '#999' }}>加载中...</span>
                    ) : (
                      articleTitle || "点击右侧按钮编辑标题"
                    )}
                  </Typography.Paragraph>
                  <Button
                    type="text"
                    className="icon-title-button custom-small-font"
                    icon={<EditOutlined style={{ fontSize: '14px' }} />}
                    onClick={startTitleEdit}
                    size="small"
                    title="编辑标题"
                  />
                  <Button
                    type="text"
                    className="icon-title-button custom-small-font"
                    icon={<ThunderboltOutlined style={{ fontSize: '14px' }} />}
                    onClick={handleGenerateTitle}
                    loading={isGeneratingTitle}
                    size="small"
                    title="AI生成标题"
                  />
                </Space>
              )}
            </Col>
          </Row>

          {/* AI总结区域 */}
          <Row style={{ margin: '10px 0', alignItems: 'flex-start' }} gutter={8}>
            <Col flex="auto">
              <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                {/* AI总结标题行 */}
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                  <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>AI总结</h1>
                  <Button
                    type="text"
                    className="icon-title-button custom-small-font"
                    icon={<ThunderboltOutlined style={{ fontSize: '14px' }} />}
                    onClick={handleGenerateSummary}
                    loading={isGeneratingSummary}
                    size="small"
                    title="AI生成总结"
                  />
                </div>

                {/* Think过程显示 */}
                {(summaryThinkContent || isSummaryThinking) && (
                  <ThinkDisplay
                    thinkContent={summaryThinkContent}
                    isThinking={isSummaryThinking}
                    isCompleted={summaryThinkCompleted}
                    style={{ marginBottom: '12px' }}
                  />
                )}

                {/* 总结内容容器 */}
                <div
                  className={`summary-content-container ${isSummaryEditing ? 'editing-mode' : ''}`}
                  style={{ flexGrow: 1, minHeight: isSummaryEditing ? '120px' : (isGeneratingSummary || articleSummary ? '80px' : '0px'), display: 'flex', flexDirection: 'column' }}
                >
                  {isSummaryEditing ? (
                    <EnhancedTextArea
                      value={editingSummaryText}
                      onChange={(e) => setEditingSummaryText(e.target.value)}
                      style={{ fontSize: '13px', width: '100%', resize: 'vertical' }}
                      placeholder="请输入总结内容"
                      autoSize={{ minRows: 4, maxRows: 20 }}
                      autoFocus
                    />
                  ) : (
                    <div style={{ minHeight: isGeneratingSummary || articleSummary ? '80px' : '0px' }}>
                      {isGeneratingSummary ? (
                        <Typography.Paragraph
                          style={{
                            fontSize: '13px',
                            marginBottom: '0',
                            lineHeight: '1.6',
                            whiteSpace: 'pre-wrap',
                            minHeight: '80px'
                          }}
                        >
                          {streamingSummaryText || '...'}
                        </Typography.Paragraph>
                      ) : articleSummary ? (
                        <Typography.Paragraph
                          style={{
                            fontSize: '13px',
                            marginBottom: '0',
                            lineHeight: '1.6',
                            whiteSpace: 'pre-wrap'
                          }}
                        >
                          {articleSummary}
                        </Typography.Paragraph>
                      ) : null}
                    </div>
                  )}
                </div>

                {/* 按钮区域 - 模仿chunk的按钮布局 */}
                <div style={{ marginTop: 'auto', display: 'flex', justifyContent: 'flex-end' }}>
                  {isSummaryEditing ? (
                    <Space style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
                      <Button icon={<SaveOutlined />} size="small" onClick={handleSaveSummaryEdit}>保存</Button>
                      <Button icon={<CloseOutlined />} size="small" onClick={cancelSummaryEdit}>取消</Button>
                    </Space>
                  ) : (
                    <Space align="baseline" size={0}>
                      <Popconfirm
                        title="确认清空总结?"
                        description="此操作将清空当前的AI总结内容。"
                        onConfirm={handleClearSummary}
                        okText="确认清空"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                        disabled={!articleSummary}
                      >
                        <Button
                          type="text"
                          danger
                          icon={<ClearOutlined />}
                          size="small"
                          title="清空总结"
                          disabled={!articleSummary}
                        />
                      </Popconfirm>
                      <Button
                        type="text"
                        icon={<EditOutlined />}
                        size="small"
                        onClick={startSummaryEdit}
                        title="编辑总结"
                      />
                    </Space>
                  )}
                </div>
              </div>
            </Col>
          </Row>

          <Divider style={{ margin: '16px 0' }} />

          {/* 内容区域标题 */}
          {!showTranslationOnly && (
            <Row style={{ margin: '10px 0', alignItems: 'center' }} gutter={16}>
              {/* 原文标题 */}
              <Col span={12}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>原文</h1>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={handleStartMergedOriginalEdit}
                    disabled={isMergedOriginalEditing}
                    size="small"
                    title="编辑原文全文"
                  />
                  <Popconfirm
                    title="确认清空原文?"
                    description="此操作将清空所有原文内容，无法撤销。"
                    onConfirm={() => {
                      setContent([]);
                      messageApi.success('原文已清空');
                    }}
                    okText="确认清空"
                    cancelText="取消"
                    okButtonProps={{ danger: true }}
                    disabled={!chunked || chunked.length === 0 || isMergedOriginalEditing}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<ClearOutlined />}
                      disabled={!chunked || chunked.length === 0 || isMergedOriginalEditing}
                      size="small"
                      title="清空原文全文"
                    />
                  </Popconfirm>
                </div>
              </Col>

              {/* 译文标题 */}
              <Col span={12}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>译文</h1>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={handleStartMergedTranslatedEdit}
                    disabled={isMergedTranslatedEditing}
                    size="small"
                    title="编辑译文全文"
                  />
                  <Popconfirm
                    title="确认清空译文?"
                    description="此操作将清空所有译文内容，但原文保留。"
                    onConfirm={handleClearAllHook}
                    okText="确认清空"
                    cancelText="取消"
                    okButtonProps={{ danger: true }}
                    disabled={!hasTranslations || isMergedTranslatedEditing}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<ClearOutlined />}
                      disabled={!hasTranslations || isMergedTranslatedEditing}
                      size="small"
                      title="清空译文全文"
                    />
                  </Popconfirm>
                </div>
              </Col>
            </Row>
          )}

            {/* 合并编辑模式或正常chunk显示模式 */}
            <Space direction="vertical" style={{ width: '100%' }} className="t-content">
              {chunked.map((items, chunkIdx) => {
                const translatedItemsForChunk = allChunkTranslatedItems[chunkIdx] || [];
                return (
                  <ChunkRenderer
                    key={`chunk-renderer-${chunkIdx}`}
                    items={items}
                    translatedItemsForChunk={translatedItemsForChunk}
                    chunkIndex={chunkIdx}
                    editingState={editingState}
                    editText={editingState.index === chunkIdx ? editText : ''}
                    debouncedSetEditText={debouncedSetEditTextHook}
                    handleEditStart={handleEditStartHook}
                    handleEditSave={handleEditSaveHook}
                    handleEditCancel={handleEditCancelHook}
                    isActivelyTranslating={isChunkTranslating(chunkIdx)}
                    disableTranslateButton={shouldDisableTranslateButton(chunkIdx)}
                    stoppingChunks={stoppingChunks}
                    handleTranslateChunk={(idx) => handleTranslateChunkHook(idx, items, shouldDisableTranslateButton)}
                    handleStopChunkTranslation={handleStopChunkTranslationHook}
                    handleClearTranslatedChunk={(idx) => handleClearTranslatedChunkHook(idx, items)}
                    handleDeleteChunk={handleDeleteChunk}
                    showTranslationOnly={showTranslationOnly}
                    renderTranslatedContentInternal={renderTranslatedContent}
                    translationContainerRefs={translationContainerRefs}
                    updateTranslationDOM={stableUpdateTranslationDOM} // Pass stable version
                    checkTagCountMismatch={checkTagCountMismatch}
                    // 传递合并编辑状态，让ChunkRenderer知道是否需要隐藏某些部分
                    isMergedOriginalEditing={isMergedOriginalEditing}
                    isMergedTranslatedEditing={isMergedTranslatedEditing}
                    // 传递合并编辑相关的数据和处理函数
                    mergedOriginalText={mergedOriginalText}
                    setMergedOriginalText={setMergedOriginalText}
                    handleSaveMergedOriginalEdit={handleSaveMergedOriginalEdit}
                    handleCancelMergedOriginalEdit={handleCancelMergedOriginalEdit}
                    mergedTranslatedText={mergedTranslatedText}
                    setMergedTranslatedText={setMergedTranslatedText}
                    handleSaveMergedTranslatedEdit={handleSaveMergedTranslatedEdit}
                    handleCancelMergedTranslatedEdit={handleCancelMergedTranslatedEdit}
                    // 传递动态高度信息
                    toolbarHeight={toolbarHeight}
                  />
                );
              })}
            </Space>

            {/* 来源区域 */}
            <Row style={{ margin: '10px 0', alignItems: 'flex-start' }} gutter={8}>
              <Col flex="auto">
                <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                  {/* 来源标题行 */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                    <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>来源</h1>
                  </div>

                  {/* 来源内容容器 */}
                  <div
                    className={`citation-content-container ${isCitationEditing ? 'editing-mode' : ''}`}
                    style={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}
                  >
                    {isCitationEditing ? (
                      <EnhancedTextArea
                        value={editingCitationText}
                        onChange={(e) => setEditingCitationText(e.target.value)}
                        style={{ fontSize: '13px', width: '100%', resize: 'vertical' }}
                        placeholder="请输入来源内容..."
                        autoSize={{ minRows: 3, maxRows: 20 }}
                        autoFocus
                      />
                    ) : (
                      <div>
                        {citationData ? (
                          <Typography.Paragraph
                            style={{
                              fontSize: '13px',
                              marginBottom: '0',
                              lineHeight: '1.6',
                              whiteSpace: 'pre-wrap'
                            }}
                          >
                            {citationData}
                          </Typography.Paragraph>
                        ) : null}
                      </div>
                    )}
                  </div>

                  {/* 按钮区域 - 模仿AI总结的按钮布局 */}
                  <div style={{ marginTop: 'auto', display: 'flex', justifyContent: citationData ? 'flex-end' : 'flex-start' }}>
                    {isCitationEditing ? (
                      <Space style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
                        <Button icon={<SaveOutlined />} size="small" onClick={handleSaveCitation}>保存</Button>
                        <Button icon={<CloseOutlined />} size="small" onClick={cancelCitationEdit}>取消</Button>
                      </Space>
                    ) : (
                      <Space align="baseline" size={0}>
                        <Popconfirm
                          title="确认清空来源?"
                          description="此操作将清空当前的来源内容。"
                          onConfirm={handleClearCitation}
                          okText="确认清空"
                          cancelText="取消"
                          okButtonProps={{ danger: true }}
                          disabled={!citationData}
                        >
                          <Button
                            type="text"
                            danger
                            icon={<ClearOutlined />}
                            size="small"
                            title="清空来源"
                            disabled={!citationData}
                          />
                        </Popconfirm>
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          size="small"
                          onClick={startCitationEdit}
                          title="编辑来源"
                        />
                      </Space>
                    )}
                  </div>
                </div>
              </Col>
            </Row>

            {/* 参考区域 */}
            <Row style={{ margin: '10px 0', alignItems: 'flex-start' }} gutter={8}>
              <Col flex="auto">
                <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                  {/* 参考标题行 */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                    <h1 style={{ fontSize: '17px', margin: '0', fontWeight: 'bold' }}>参考</h1>
                  </div>

                  {/* 参考内容容器 */}
                  <div
                    className={`refs-content-container ${isRefsEditing ? 'editing-mode' : ''}`}
                    style={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}
                  >
                    {isRefsEditing ? (
                      <EnhancedTextArea
                        value={editingRefsText}
                        onChange={(e) => setEditingRefsText(e.target.value)}
                        style={{ fontSize: '13px', width: '100%', resize: 'vertical' }}
                        placeholder="请输入参考内容..."
                        autoSize={{ minRows: 3, maxRows: 20 }}
                        autoFocus
                      />
                    ) : (
                      <div>
                        {refsData ? (
                          <Typography.Paragraph
                            style={{
                              fontSize: '13px',
                              marginBottom: '0',
                              lineHeight: '1.6',
                              whiteSpace: 'pre-wrap'
                            }}
                          >
                            {refsData}
                          </Typography.Paragraph>
                        ) : null}
                      </div>
                    )}
                  </div>

                  {/* 按钮区域 - 模仿AI总结的按钮布局 */}
                  <div style={{ marginTop: 'auto', display: 'flex', justifyContent: refsData ? 'flex-end' : 'flex-start' }}>
                    {isRefsEditing ? (
                      <Space style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
                        <Button
                          icon={<RobotOutlined />}
                          size="small"
                          onClick={handleParseRefs}
                          loading={isParsingRefs}
                          disabled={!editingRefsText.trim()}
                        >
                          AI格式化
                        </Button>
                        <Button icon={<SaveOutlined />} size="small" onClick={handleSaveRefs}>保存</Button>
                        <Button icon={<CloseOutlined />} size="small" onClick={cancelRefsEdit}>取消</Button>
                      </Space>
                    ) : (
                      <Space align="baseline" size={0}>
                        <Popconfirm
                          title="确认清空参考?"
                          description="此操作将清空当前的参考内容。"
                          onConfirm={handleClearRefs}
                          okText="确认清空"
                          cancelText="取消"
                          okButtonProps={{ danger: true }}
                          disabled={!refsData}
                        >
                          <Button
                            type="text"
                            danger
                            icon={<ClearOutlined />}
                            size="small"
                            title="清空参考"
                            disabled={!refsData}
                          />
                        </Popconfirm>
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          size="small"
                          onClick={startRefsEdit}
                          title="编辑参考"
                        />
                      </Space>
                    )}
                  </div>
                </div>
              </Col>
            </Row>
        </div>
      )}
      {/* 术语同步模态框 */}
      <TerminologySyncModal
        open={isTerminologyModalOpen}
        terms={terminologyListForModal}
        progress={terminologyExtractionProgress}
        onConfirm={applyTerminologySync} // 直接使用 store 中的 action
        onCancel={closeTerminologyModal} // 直接使用 store 中的 action
      />


    </div>
  );
};

export default memo(ArticleEditPage);
