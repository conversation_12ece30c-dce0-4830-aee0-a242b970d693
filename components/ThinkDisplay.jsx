import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button, Typography, Collapse } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Text } = Typography;

const ThinkDisplay = ({
  thinkContent,
  isThinking = false,
  isCompleted = false,
  style = {}
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [displayContent, setDisplayContent] = useState('');
  const contentRef = useRef(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const scrollTimeoutRef = useRef(null);
  const isAutoScrollingRef = useRef(false);
  const scrollDebounceRef = useRef(null);

  useEffect(() => {
    if (thinkContent) {
      setDisplayContent(thinkContent);
    }
  }, [thinkContent]);

  // 检测用户是否手动滚动（使用防抖）
  const handleScroll = useCallback(() => {
    if (!contentRef.current || isAutoScrollingRef.current) return;

    // 清除之前的防抖定时器
    if (scrollDebounceRef.current) {
      clearTimeout(scrollDebounceRef.current);
    }

    // 防抖处理，避免频繁状态更新
    scrollDebounceRef.current = setTimeout(() => {
      if (!contentRef.current || isAutoScrollingRef.current) return;

      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      const isAtBottom = Math.abs(scrollHeight - clientHeight - scrollTop) < 5; // 5px 容差

      // 如果用户滚动到非底部位置，标记为用户滚动
      if (!isAtBottom) {
        setIsUserScrolling(true);
        // 清除之前的定时器
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
        // 3秒后重置用户滚动状态，允许自动滚动
        scrollTimeoutRef.current = setTimeout(() => {
          setIsUserScrolling(false);
        }, 3000);
      } else {
        // 如果用户滚动回底部，立即允许自动滚动
        setIsUserScrolling(false);
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      }
    }, 50); // 50ms 防抖延迟
  }, []);

  // 当内容更新且正在思考时，智能自动滚动到底部
  useEffect(() => {
    if (contentRef.current && isThinking && isExpanded && displayContent && !isUserScrolling) {
      // 标记正在自动滚动，防止触发scroll事件处理
      isAutoScrollingRef.current = true;

      // 使用 setTimeout 确保 DOM 更新完成后再滚动
      setTimeout(() => {
        if (contentRef.current && !isUserScrolling) {
          contentRef.current.scrollTop = contentRef.current.scrollHeight;

          // 滚动完成后，短暂延迟后重置标记
          setTimeout(() => {
            isAutoScrollingRef.current = false;
          }, 100);
        } else {
          isAutoScrollingRef.current = false;
        }
      }, 0);
    }
  }, [displayContent, isThinking, isExpanded, isUserScrolling]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      if (scrollDebounceRef.current) {
        clearTimeout(scrollDebounceRef.current);
      }
    };
  }, []);

  // 如果没有 think 内容且不在思考中，不显示组件
  if (!thinkContent && !isThinking) {
    return null;
  }

  // 如果思考完成，自动折叠
  useEffect(() => {
    if (isCompleted && isThinking === false) {
      setIsExpanded(false);
    }
  }, [isCompleted, isThinking]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div
      style={{
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        marginBottom: '12px',
        backgroundColor: '#fafafa',
        ...style
      }}
    >
      {/* Think 标题栏 */}
      <div
        style={{
          padding: '8px 12px',
          borderBottom: isExpanded ? '1px solid #d9d9d9' : 'none',
          backgroundColor: '#f0f0f0',
          borderRadius: isExpanded ? '6px 6px 0 0' : '6px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          cursor: 'pointer'
        }}
        onClick={toggleExpanded}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Text
            style={{
              fontSize: '12px',
              fontWeight: 'bold',
              color: '#666'
            }}
          >
            {isThinking ? 'AI 思考中...' : 'AI 思考过程'}
          </Text>
        </div>
        <Button
          type="text"
          size="small"
          icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
          style={{
            fontSize: '10px',
            color: '#666',
            padding: '0 4px'
          }}
        />
      </div>

      {/* Think 内容区域 */}
      {isExpanded && (
        <div
          ref={contentRef}
          onScroll={handleScroll}
          style={{
            padding: '12px',
            maxHeight: '300px',
            overflowY: 'auto',
            fontSize: '12px',
            lineHeight: '1.5',
            color: '#666',
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            whiteSpace: 'pre-wrap',
            backgroundColor: '#fff'
          }}
        >
          {displayContent || (isThinking ? '思考中...' : '暂无思考内容')}
        </div>
      )}


    </div>
  );
};

export default ThinkDisplay;
